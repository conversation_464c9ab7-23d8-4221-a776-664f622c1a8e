"""Validation script for MNIST models (MLP and CNN) with checkpointing and CLI support."""
import argparse
import logging
import os

from PIL import Image
import torch
from torch.utils.data import DataLoader, Subset
from torchvision import datasets, transforms

from mnist_cnn import MinimalMNISTCNN
from mnist_train import SimpleMNISTModel, load_latest_checkpoint

# All imports should be at the top of the file, not inside functions or classes.
# Configure logging
LOG_LEVEL = os.environ.get("MNIST_LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    format="%(asctime)s [%(levelname)s] %(message)s",
    level=getattr(logging, LOG_LEVEL, logging.INFO),
)
logger = logging.getLogger("mnist_validate")


class PatchedMNIST(datasets.MNIST):
    """Patches the MNIST dataset to handle image loading and transformation."""

    def __getitem__(self, index):
        img, target = self.data[index], int(self.targets[index])
        img = Image.fromarray(img.numpy()).convert("L")
        if self.transform is not None:
            img = self.transform(img)
        if self.target_transform is not None:
            target = self.target_transform(target)
        return img, target


def get_full_test_loader(
    batch_size: int = 64, data_dir: str = "./mnist_data"
) -> DataLoader:
    """
    Creates a DataLoader for the full MNIST test dataset.

    Args:
        batch_size (int): The size of each batch.
        data_dir (str): The directory where MNIST data is stored.

    Returns:
        DataLoader: A DataLoader object for the test dataset.
    """
    transform = transforms.Compose(
        [
            transforms.ToTensor(),
        ]
    )
    test_dataset = PatchedMNIST(
        root=data_dir, train=False, download=True, transform=transform
    )
    loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    return loader


def validate_main(args=None) -> None:  # pylint: disable=too-many-locals
    """
    Main validation script for MNIST models.

    Args:
        args (argparse.Namespace): Command line arguments. If None, parses arguments.
    Too many locals warning is suppressed for CLI clarity and maintainability.
    """
    if args is None:
        parser = argparse.ArgumentParser(description="MNIST validation script.")
        parser.add_argument(
            "--test-mode", action="store_true", help="Use minimal data for testing."
        )
        parser.add_argument(
            "--model",
            type=str,
            default="mlp",
            choices=["mlp", "cnn"],
            help="Model architecture: 'mlp' (default) or 'cnn'",
        )
        parser.add_argument(
            "--batch-size", type=int, default=64, help="Batch size for validation."
        )
        parser.add_argument(
            "--data-dir",
            type=str,
            default="./mnist_data",
            help="Directory for MNIST data.",
        )
        parser.add_argument(
            "--checkpoint-dir",
            type=str,
            default="./checkpoints",
            help="Directory for checkpoints.",
        )
        parser.add_argument(
            "--conv1-out",
            type=int,
            default=16,
            help="Number of output channels for first conv layer (CNN only)",
        )
        parser.add_argument(
            "--conv2-out",
            type=int,
            default=32,
            help="Number of output channels for second conv layer (CNN only)",
        )
        parser.add_argument(
            "--conv3-out",
            type=int,
            default=32,
            help="Number of output channels for third conv layer (CNN only)",
        )
        parser.add_argument(
            "--fc-out",
            type=int,
            default=10,
            help="Number of output features for final FC layer (CNN only)",
        )
        parser.add_argument(
            "--compile",
            action="store_true",
            help="Use torch.compile for the model (PyTorch 2.x+)",
        )
        args = parser.parse_args()
    if getattr(args, "model", "mlp") == "cnn":
        model = MinimalMNISTCNN(
            conv1_out=args.conv1_out,
            conv2_out=args.conv2_out,
            conv3_out=args.conv3_out,
            fc_out=args.fc_out,
        )
        model_type = "cnn"
        logger.info(
            "Using MinimalMNISTCNN (compact CNN) model: conv1_out=%d, conv2_out=%d, "
            "conv3_out=%d, fc_out=%d",
            args.conv1_out,
            args.conv2_out,
            args.conv3_out,
            args.fc_out,
        )
    else:
        model = SimpleMNISTModel()
        model_type = "mlp"
        logger.info("Using SimpleMNISTModel (MLP) model.")
    # Optionally compile the model
    if getattr(args, "compile", False):
        if hasattr(torch, "compile"):
            logger.info("Compiling model with torch.compile...")
            model = torch.compile(model)
        else:
            logger.warning(
                "torch.compile is not available in this PyTorch version. Skipping compilation."
            )
    # Optimizer is required only for loading checkpoint state, not for validation
    optimizer = torch.optim.SGD(model.parameters(), lr=0.01)
    start_epoch = load_latest_checkpoint(
        model, optimizer, checkpoint_dir=args.checkpoint_dir, model_type=model_type
    )
    if start_epoch is None:
        return
    model.eval()
    if args.test_mode:
        transform = transforms.Compose(
            [
                transforms.ToTensor(),
            ]
        )
        test_dataset = PatchedMNIST(
            root=args.data_dir, train=False, download=True, transform=transform
        )
        test_dataset = Subset(test_dataset, range(8))
        test_loader = DataLoader(
            test_dataset, batch_size=args.batch_size, shuffle=False
        )
    else:
        test_loader = get_full_test_loader(
            batch_size=args.batch_size, data_dir=args.data_dir
        )
    correct = 0
    total = 0
    logger.info("Running validation on the MNIST test set...")
    with torch.no_grad():
        for images, labels in test_loader:
            outputs = model(images)
            preds = outputs.argmax(dim=1)
            correct += (preds == labels).sum().item()
            total += labels.size(0)
    accuracy = correct / total if total > 0 else 0.0
    logger.info("Validation accuracy on %d test samples: %.4f", total, accuracy)


if __name__ == "__main__":
    try:
        validate_main()
    except KeyboardInterrupt:
        logger.warning("Validation interrupted by user.")
    except Exception as e:  # pylint: disable=broad-exception-caught
        # Catching all exceptions to ensure uncaught errors are logged in CLI usage.
        logger.error("Uncaught exception: %s", e, exc_info=True)
        print(f"[ERROR] {e}")
