"""Training script for MNIST models (MLP and CNN) with checkpointing and CLI support.

This script provides training routines, checkpointing, and CLI for both MLP and CNN models
on MNIST.
"""

import glob
import logging
import os
import time
from datetime import datetime
import argparse

from PIL import Image
import torch
from torch import nn, optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Subset
from torchvision import datasets, transforms

from mnist_cnn import MinimalMNISTCNN

# Configure logging
LOG_LEVEL = os.environ.get("MNIST_LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    format="%(asctime)s [%(levelname)s] %(message)s",
    level=getattr(logging, LOG_LEVEL, logging.INFO),
)
logger = logging.getLogger("mnist_train")


class PatchedMNIST(datasets.MNIST):
    """Patched MNIST dataset to ensure compatibility with PIL transforms."""

    def __getitem__(self, index):
        img, target = self.data[index], int(self.targets[index])
        img = Image.fromarray(img.numpy()).convert("L")
        if self.transform is not None:
            img = self.transform(img)
        if self.target_transform is not None:
            target = self.target_transform(target)
        return img, target


def get_mnist_dataloaders(
    data_dir: str = "./mnist_data", batch_size: int = 64
) -> tuple:
    """
    Download and load the MNIST dataset, returning train and test DataLoaders.
    Args:
        data_dir (str): Directory to store/download the MNIST data.
        batch_size (int): Batch size for the DataLoaders.
    Returns:
        train_loader, test_loader: DataLoader objects for training and testing.
    """
    # Define a simple transform to convert images to tensors
    transform = transforms.Compose(
        [
            transforms.ToTensor(),
        ]
    )
    # Download and load the training set
    train_dataset = PatchedMNIST(
        root=data_dir, train=True, download=True, transform=transform
    )
    # Download and load the test set
    test_dataset = PatchedMNIST(
        root=data_dir, train=False, download=True, transform=transform
    )
    # Create DataLoaders for batching
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    return train_loader, test_loader


class SimpleMNISTModel(nn.Module):
    """
    A simple feedforward neural network for MNIST digit classification.
    Architecture: Flatten -> Linear(784, 128) -> ReLU -> Linear(128, 10)
    """

    def __init__(self):
        super().__init__()
        self.fc1 = nn.Linear(28 * 28, 128)  # First fully connected layer
        self.fc2 = nn.Linear(128, 10)  # Output layer for 10 classes

    def forward(self, x):
        """Forward pass for the SimpleMNISTModel."""
        # Flatten the input tensor (batch_size, 1, 28, 28) -> (batch_size, 784)
        x = x.view(x.size(0), -1)
        x = F.relu(self.fc1(x))
        x = self.fc2(x)
        return x


def get_model_config(model, model_type):
    """Return model configuration dictionary for checkpointing and validation."""
    if model_type == "cnn":
        return {
            "conv1_out": (
                getattr(model, "conv1", None)[0].out_channels
                if hasattr(model, "conv1")
                else None
            ),
            "conv2_out": (
                getattr(model, "conv2", None)[0].out_channels
                if hasattr(model, "conv2")
                else None
            ),
            "conv3_out": (
                getattr(model, "conv3", None)[0].out_channels
                if hasattr(model, "conv3")
                else None
            ),
            "fc_out": model.fc1.out_features if hasattr(model, "fc1") else None,
        }
    return {"type": "mlp"}


def save_checkpoint(
    model, optimizer, epoch, checkpoint_dir="./checkpoints", tag=None, model_type="mlp"
):  # pylint: disable=too-many-arguments,too-many-positional-arguments
    """
    Save model and optimizer state as a checkpoint.
    Too many arguments/positional-arguments disables are justified for flexibility in CLI usage.
    """
    os.makedirs(checkpoint_dir, exist_ok=True)
    if tag is None:
        checkpoint_path = os.path.join(
            checkpoint_dir, f"mnist_{model_type}_epoch_{epoch}.pt"
        )
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        checkpoint_path = os.path.join(
            checkpoint_dir, f"mnist_{model_type}_epoch_{epoch}_{tag}_{timestamp}.pt"
        )
    torch.save(
        {
            "epoch": epoch,
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": optimizer.state_dict(),
            "model_config": get_model_config(model, model_type),
        },
        checkpoint_path,
    )
    logger.info("Checkpoint saved: %s", checkpoint_path)


def load_latest_checkpoint(
    model, optimizer, checkpoint_dir="./checkpoints", model_type="mlp"
):
    """
    Load the latest checkpoint if available.
    Args:
        model: The model to load state into.
        optimizer: The optimizer to load state into.
        checkpoint_dir (str): Directory where checkpoints are stored.
        model_type (str): Model type string for filename (e.g., 'mlp', 'cnn').
    Returns:
        start_epoch (int): The epoch to resume from (next epoch).
    """
    pattern = os.path.join(checkpoint_dir, f"mnist_{model_type}_epoch_*.pt")
    checkpoint_files = glob.glob(pattern)
    if not checkpoint_files:
        logger.info("No checkpoint found. Starting from scratch.")
        return 1  # Start from epoch 1 if no checkpoint
    latest_ckpt = max(checkpoint_files, key=os.path.getctime)
    checkpoint = torch.load(latest_ckpt)
    # Check model config
    checkpoint_config = checkpoint.get("model_config", None)
    current_config = get_model_config(model, model_type)
    if checkpoint_config != current_config:
        logger.error(
            "Model architecture mismatch!\nCheckpoint config: %s\nCurrent config: %s\n"
            "Aborting load.",
            checkpoint_config,
            current_config,
        )
        raise ValueError("Model architecture does not match checkpoint.")
    model.load_state_dict(checkpoint["model_state_dict"])
    optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
    logger.info("Loaded checkpoint: %s", latest_ckpt)
    return checkpoint["epoch"] + 1  # Resume from next epoch


def train_model(
    model,
    train_loader,
    test_loader,
    epochs=3,
    lr=0.01,
    device=None,
    checkpoint_dir="./checkpoints",
    save_every_minutes=None,
    model_type="mlp",
):  # pylint: disable=too-many-arguments,too-many-locals,too-many-statements,too-many-positional-arguments
    """
    Train the model on the MNIST dataset.
    Args:
        model: The neural network to train.
        train_loader: DataLoader for training data.
        test_loader: DataLoader for test data.
        epochs (int): Number of training epochs.
        lr (float): Learning rate.
        device: torch.device to use (CPU by default).
        checkpoint_dir (str): Directory to save checkpoints.
        save_every_minutes (float or None): If set, save a checkpoint every n minutes
            during training.
        model_type (str): Model type string for filename (e.g., 'mlp', 'cnn').
    """
    if device is None:
        device = torch.device("cpu")
    model.to(device)
    optimizer = optim.SGD(model.parameters(), lr=lr)
    # Load latest checkpoint if available
    start_epoch = load_latest_checkpoint(
        model, optimizer, checkpoint_dir, model_type=model_type
    )
    criterion = nn.CrossEntropyLoss()
    last_save_time = time.time()
    for epoch in range(start_epoch, epochs + 1):
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0
        for batch_idx, (images, labels) in enumerate(train_loader):
            images, labels = images.to(device), labels.to(device)
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            running_loss += loss.item() * images.size(0)
            _, predicted = outputs.max(1)
            correct += predicted.eq(labels).sum().item()
            total += labels.size(0)
            # Save checkpoint every n minutes if requested
            if save_every_minutes is not None:
                now = time.time()
                if now - last_save_time >= save_every_minutes * 60:
                    save_checkpoint(
                        model,
                        optimizer,
                        epoch,
                        checkpoint_dir,
                        tag=f"batch{batch_idx}",
                        model_type=model_type,
                    )
                    last_save_time = now
        train_loss = running_loss / total
        train_acc = correct / total
        # Validation
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        with torch.no_grad():
            for images, labels in test_loader:
                images, labels = images.to(device), labels.to(device)
                outputs = model(images)
                loss = criterion(outputs, labels)
                val_loss += loss.item() * images.size(0)
                _, predicted = outputs.max(1)
                val_correct += predicted.eq(labels).sum().item()
                val_total += labels.size(0)
        val_loss /= val_total
        val_acc = val_correct / val_total
        log_epoch_summary(
            epoch,
            train_loss,
            train_acc,
            val_loss,
            val_acc,
        )
        save_checkpoint(model, optimizer, epoch, checkpoint_dir, model_type=model_type)


def evaluate_model(model, data_loader, device=None):
    """
    Evaluate the model on a dataset and return accuracy.
    Args:
        model: The neural network to evaluate.
        data_loader: DataLoader for the dataset to evaluate on.
        device: torch.device to use (CPU by default).
    Returns:
        accuracy (float): Fraction of correct predictions.
    """
    if device is None:
        device = torch.device("cpu")
    model.to(device)
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for images, labels in data_loader:
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            _, predicted = outputs.max(1)
            correct += predicted.eq(labels).sum().item()
            total += labels.size(0)
    accuracy = correct / total if total > 0 else 0.0
    return accuracy


def log_epoch_summary(epoch, train_loss, train_acc, val_loss, val_acc):
    """Log epoch summary in a Pylint-compliant way."""
    logger.info(
        "Epoch %d: Train Loss=%f, Train Acc=%f, Val Loss=%f, Val Acc=%f",
        epoch,
        train_loss,
        train_acc,
        val_loss,
        val_acc,
    )


# pylint: disable=too-many-locals,too-many-statements
def train_main(
    args=None,
) -> None:
    # Disable warnings for CLI main function - complexity is acceptable for argument parsing
    # and orchestration
    """
    Main CLI entry point for training MNIST models with checkpointing and time limits.
    Handles argument parsing, data loading, model selection, and time-limited training loop.
    Too many locals/statements warning is suppressed for CLI clarity and maintainability.
    """
    if args is None:
        parser = argparse.ArgumentParser(
            description="Train MNIST model with checkpointing and time limit."
        )
        parser.add_argument(
            "--minutes",
            type=float,
            default=1,
            help="Number of minutes to train (default: 1)",
        )
        parser.add_argument(
            "--test-mode",
            action="store_true",
            help="Use minimal data and fast settings for testing.",
        )
        parser.add_argument(
            "--model",
            type=str,
            default="mlp",
            choices=["mlp", "cnn"],
            help="Model architecture: 'mlp' (default) or 'cnn'",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=64,
            help="Batch size for training and validation.",
        )
        parser.add_argument("--lr", type=float, default=0.01, help="Learning rate.")
        parser.add_argument(
            "--data-dir",
            type=str,
            default="./mnist_data",
            help="Directory for MNIST data.",
        )
        parser.add_argument(
            "--checkpoint-dir",
            type=str,
            default="./checkpoints",
            help="Directory for checkpoints.",
        )
        parser.add_argument(
            "--compile",
            action="store_true",
            help="Use torch.compile for the model (PyTorch 2.x+)",
        )
        parser.add_argument(
            "--conv1-out",
            type=int,
            default=16,
            help="Number of output channels for first conv layer (CNN only)",
        )
        parser.add_argument(
            "--conv2-out",
            type=int,
            default=32,
            help="Number of output channels for second conv layer (CNN only)",
        )
        parser.add_argument(
            "--conv3-out",
            type=int,
            default=32,
            help="Number of output channels for third conv layer (CNN only)",
        )
        parser.add_argument(
            "--fc-out",
            type=int,
            default=10,
            help="Number of output features for final FC layer (CNN only)",
        )
        args = parser.parse_args()
    if args.test_mode:
        transform = transforms.Compose(
            [
                transforms.ToTensor(),
            ]
        )
        train_dataset = PatchedMNIST(
            root=args.data_dir, train=True, download=True, transform=transform
        )
        test_dataset = PatchedMNIST(
            root=args.data_dir, train=False, download=True, transform=transform
        )
        train_dataset = Subset(train_dataset, range(8))
        test_dataset = Subset(test_dataset, range(8))
        train_loader = DataLoader(
            train_dataset, batch_size=args.batch_size, shuffle=True
        )
        test_loader = DataLoader(
            test_dataset, batch_size=args.batch_size, shuffle=False
        )
        max_minutes = 0.01
    else:
        train_loader, test_loader = get_mnist_dataloaders(
            data_dir=args.data_dir, batch_size=args.batch_size
        )
        max_minutes = args.minutes
    if getattr(args, "model", "mlp") == "cnn":
        model = MinimalMNISTCNN(
            conv1_out=args.conv1_out,
            conv2_out=args.conv2_out,
            conv3_out=args.conv3_out,
            fc_out=args.fc_out,
        )
        model_type = "cnn"
        logger.info(
            "Using MinimalMNISTCNN (compact CNN) model: conv1_out=%d, conv2_out=%d, "
            "conv3_out=%d, fc_out=%d",
            args.conv1_out,
            args.conv2_out,
            args.conv3_out,
            args.fc_out,
        )
    else:
        model = SimpleMNISTModel()
        model_type = "mlp"
        logger.info("Using SimpleMNISTModel (MLP) model.")
    # Optionally compile the model
    if getattr(args, "compile", False):
        if hasattr(torch, "compile"):
            logger.info("Compiling model with torch.compile...")
            model = torch.compile(model)
        else:
            logger.warning(
                "torch.compile is not available in this PyTorch version. Skipping compilation."
            )
    start_time = time.time()
    max_seconds = max_minutes * 60
    epochs = 100
    checkpoint_dir = args.checkpoint_dir
    save_every_minutes = 0.5

    def time_limited_train():
        for epoch in range(1, epochs + 1):
            if time.time() - start_time > max_seconds:
                logger.warning("Training stopped after %f minute(s).", max_minutes)
                break
            train_model(
                model,
                train_loader,
                test_loader,
                epochs=epoch,
                lr=args.lr,
                device=None,
                checkpoint_dir=checkpoint_dir,
                save_every_minutes=save_every_minutes,
                model_type=model_type,
            )

    time_limited_train()
    logger.info("Training session complete.")


if __name__ == "__main__":
    try:
        train_main()
    except KeyboardInterrupt:
        logger.warning("Training interrupted by user.")
    except Exception as e:  # pylint: disable=broad-exception-caught
        # Catching all exceptions to ensure uncaught errors are logged in CLI usage.
        logger.error("Uncaught exception: %s", e, exc_info=True)
        print(f"[ERROR] {e}")
