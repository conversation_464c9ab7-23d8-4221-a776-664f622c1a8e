"""Minimal CNN model and helpers for MNIST digit classification.

This module provides a minimal convolutional neural network (CNN) architecture for MNIST 
digit classification, along with helper functions for building convolutional blocks, 
training for one epoch, and evaluating model accuracy.
All functions and classes are fully type-annotated and documented for clarity and Pylint compliance.
"""
# Group and order imports: standard library, third-party, then local imports.
# Alphabetize within each group. No imports inside functions/classes.
from typing import Optional

import torch
from torch import nn

# All imports should be at the top of the file, not inside functions or classes.


def conv_block(
    in_channels: int,
    out_channels: int,
    kernel_size: int,
    pool: bool = False,
    batch_norm: bool = False,
) -> nn.Sequential:
    """
    Create a convolutional block with optional pooling and batch normalization.

    Args:
        in_channels (int): Number of input channels.
        out_channels (int): Number of output channels.
        kernel_size (int): Size of the convolution kernel.
        pool (bool, optional): Whether to add MaxPool2d(2) after conv+relu. Defaults to False.
        batch_norm (bool, optional): Whether to add BatchNorm2d after conv. Defaults to False.

    Returns:
        nn.Sequential: The convolutional block.
    """
    layers = [nn.Conv2d(in_channels, out_channels, kernel_size, padding=1)]
    if batch_norm:
        layers.append(nn.BatchNorm2d(out_channels))
    layers.append(nn.ReLU(inplace=True))
    if pool:
        layers.append(nn.MaxPool2d(2))
    return nn.Sequential(*layers)


class MinimalMNISTCNN(nn.Module):
    """
    Minimal CNN for MNIST digit classification.

    Architecture: Conv-BN-ReLU-Pool x2 -> Conv-BN-ReLU -> FC

    Args:
        conv1_out (int, optional): Output channels for first conv layer. Defaults to 16.
        conv2_out (int, optional): Output channels for second conv layer. Defaults to 32.
        conv3_out (int, optional): Output channels for third conv layer. Defaults to 32.
        fc_out (int, optional): Output features for final fully connected layer. Defaults to 10.
    """

    def __init__(
        self,
        conv1_out: int = 16,
        conv2_out: int = 32,
        conv3_out: int = 32,
        fc_out: int = 10,
    ):
        super().__init__()
        self.conv1 = conv_block(1, conv1_out, 3, pool=True, batch_norm=True)
        self.conv2 = conv_block(conv1_out, conv2_out, 3, pool=True, batch_norm=True)
        self.conv3 = conv_block(conv2_out, conv3_out, 3, pool=False, batch_norm=True)
        self.fc1 = nn.Linear(conv3_out * 7 * 7, fc_out)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass for the CNN.

        Args:
            x (torch.Tensor): Input tensor of shape (batch, 1, 28, 28).

        Returns:
            torch.Tensor: Output logits of shape (batch, fc_out).
        """
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.conv3(x)
        x = x.view(x.size(0), -1)
        x = self.fc1(x)
        return x


def train_one_epoch(
    model: nn.Module,
    loader: torch.utils.data.DataLoader,
    optimizer: torch.optim.Optimizer,
    device: Optional[torch.device] = None,
) -> None:
    """
    Train the model for one epoch.

    Args:
        model (nn.Module): The model to train.
        loader (DataLoader): DataLoader for training data.
        optimizer (Optimizer): Optimizer for model parameters.
        device (torch.device, optional): Device to use (default: CPU).
    """
    if device is None:
        device = torch.device("cpu")
    model.train()
    criterion = nn.CrossEntropyLoss()
    for images, labels in loader:
        images, labels = images.to(device), labels.to(device)
        optimizer.zero_grad()
        outputs = model(images)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()


def evaluate_model(
    model: nn.Module,
    loader: torch.utils.data.DataLoader,
    device: Optional[torch.device] = None,
) -> float:
    """
    Evaluate the model and return accuracy.

    Args:
        model (nn.Module): The model to evaluate.
        loader (DataLoader): DataLoader for evaluation data.
        device (torch.device, optional): Device to use (default: CPU).

    Returns:
        float: Accuracy (0.0 to 1.0).
    """
    if device is None:
        device = torch.device("cpu")
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for images, labels in loader:
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            _, predicted = outputs.max(1)
            correct += predicted.eq(labels).sum().item()
            total += labels.size(0)
    return correct / total if total > 0 else 0.0
