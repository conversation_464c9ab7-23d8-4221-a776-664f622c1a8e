"""Inference script for MNIST models (MLP and CNN) with checkpointing and CLI support."""
import argparse
import logging
import os

import torch
from torch import optim
from torch.utils.data import DataLoader, Subset
from torchvision import transforms

from mnist_cnn import MinimalMNISTCNN
from mnist_train import SimpleMNISTModel, load_latest_checkpoint
from test_helpers import PatchedMNIST  # Use shared PatchedMNIST

# Configure logging
LOG_LEVEL = os.environ.get("MNIST_LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    format="%(asctime)s [%(levelname)s] %(message)s",
    level=getattr(logging, LOG_LEVEL, logging.INFO),
)
logger = logging.getLogger("mnist_infer")


def get_test_samples(
    num_samples: int = 5, data_dir: str = "./mnist_data"
) -> DataLoader:
    """
    Loads a subset of MNIST test samples for inference.
    Args:
        num_samples (int): The number of samples to load.
        data_dir (str): The directory containing the MNIST data.
    Returns:
        DataLoader: A DataLoader object for the subset of test samples.
    """
    transform = transforms.Compose(
        [
            transforms.ToTensor(),
        ]
    )
    test_dataset = PatchedMNIST(
        root=data_dir, train=False, download=True, transform=transform
    )
    indices = list(range(num_samples))
    subset = Subset(test_dataset, indices)
    loader = DataLoader(subset, batch_size=1, shuffle=False)
    return loader


def infer_main(args=None) -> None:
    """
    Main inference function. Handles command line arguments, model loading, and inference loop.
    Args:
        args (argparse.Namespace): Command line arguments. If None, parses arguments.
    """
    if args is None:
        parser = argparse.ArgumentParser(description="MNIST inference demo.")
        parser.add_argument(
            "--test-mode", action="store_true", help="Use minimal data for testing."
        )
        parser.add_argument(
            "--model",
            type=str,
            default="mlp",
            choices=["mlp", "cnn"],
            help="Model architecture: 'mlp' (default) or 'cnn'",
        )
        parser.add_argument(
            "--batch-size", type=int, default=1, help="Batch size for inference."
        )
        parser.add_argument(
            "--data-dir",
            type=str,
            default="./mnist_data",
            help="Directory for MNIST data.",
        )
        parser.add_argument(
            "--checkpoint-dir",
            type=str,
            default="./checkpoints",
            help="Directory for checkpoints.",
        )
        parser.add_argument(
            "--conv1-out",
            type=int,
            default=16,
            help="Number of output channels for first conv layer (CNN only)",
        )
        parser.add_argument(
            "--conv2-out",
            type=int,
            default=32,
            help="Number of output channels for second conv layer (CNN only)",
        )
        parser.add_argument(
            "--conv3-out",
            type=int,
            default=32,
            help="Number of output channels for third conv layer (CNN only)",
        )
        parser.add_argument(
            "--fc-out",
            type=int,
            default=10,
            help="Number of output features for final FC layer (CNN only)",
        )
        parser.add_argument(
            "--compile",
            action="store_true",
            help="Use torch.compile for the model (PyTorch 2.x+)",
        )
        args = parser.parse_args()
    if getattr(args, "model", "mlp") == "cnn":
        model = MinimalMNISTCNN(
            conv1_out=args.conv1_out,
            conv2_out=args.conv2_out,
            conv3_out=args.conv3_out,
            fc_out=args.fc_out,
        )
        model_type = "cnn"
        logger.info(
            "Using MinimalMNISTCNN (compact CNN) model: conv1_out=%d, conv2_out=%d, "
            "conv3_out=%d, fc_out=%d",
            args.conv1_out,
            args.conv2_out,
            args.conv3_out,
            args.fc_out,
        )
    else:
        model = SimpleMNISTModel()
        model_type = "mlp"
        logger.info("Using SimpleMNISTModel (MLP) model.")
    # Optionally compile the model
    if getattr(args, "compile", False):
        if hasattr(torch, "compile"):
            logger.info("Compiling model with torch.compile...")
            model = torch.compile(model)
        else:
            logger.warning(
                "torch.compile is not available in this PyTorch version. Skipping compilation."
            )
    # Optimizer is required only for loading checkpoint state, not for inference
    optimizer = optim.SGD(model.parameters(), lr=0.01)
    start_epoch = load_latest_checkpoint(
        model, optimizer, checkpoint_dir=args.checkpoint_dir, model_type=model_type
    )
    if start_epoch is None:
        return
    model.eval()
    num_samples = 8 if args.test_mode else 5
    test_loader = get_test_samples(num_samples=num_samples, data_dir=args.data_dir)
    logger.info("Running inference on %d MNIST test samples:", num_samples)
    with torch.no_grad():
        for idx, (img, label) in enumerate(test_loader):
            output = model(img)
            pred = output.argmax(dim=1).item()
            logger.info(
                "Sample %d: True label = %d, Predicted = %d",
                idx + 1,
                label.item(),
                pred,
            )


if __name__ == "__main__":
    try:
        infer_main()
    except KeyboardInterrupt:
        logger.warning("Inference interrupted by user.")
    except Exception as e:  # pylint: disable=broad-exception-caught
        # Catching all exceptions to ensure uncaught errors are logged in CLI usage.
        logger.error("Uncaught exception: %s", e, exc_info=True)
        print(f"[ERROR] {e}")
