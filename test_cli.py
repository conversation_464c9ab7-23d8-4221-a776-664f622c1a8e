"""Test suite for CLI entry points and helpers for MNIST project.

This file contains tests for CLI entry points, helpers, and integration scenarios for the MNIST
project. All tests are Pylint-compliant and follow best practices for import order, docstrings,
and argument usage.
"""

# pylint: disable=redefined-outer-name, import-outside-toplevel, too-few-public-methods
# pylint: disable=too-many-arguments, too-many-positional-arguments, unused-argument, useless-return
# pylint: disable=reimported
# Disable warnings for test isolation - test functions require local imports for proper mocking
# These disables are justified for pytest test code patterns
import os
import shutil
import subprocess
import sys
from types import SimpleNamespace

import pytest

from mnist_train import train_main
from mnist_infer import infer_main
from mnist_validate import validate_main


def _temporarily_move_checkpoints():
    """
    Temporarily move the checkpoints directory for test isolation.
    Returns:
        tuple: (moved, ckpt_dir, tmp_dir)
    """
    ckpt_dir = "./checkpoints"
    tmp_dir = "./checkpoints_tmp"
    if os.path.exists(ckpt_dir):
        shutil.move(ckpt_dir, tmp_dir)
        return True, ckpt_dir, tmp_dir
    return False, ckpt_dir, tmp_dir


def _restore_checkpoints(moved, ckpt_dir, tmp_dir):
    """
    Restore the checkpoints directory after test isolation.
    Args:
        moved (bool): Whether the directory was moved.
        ckpt_dir (str): Path to checkpoints dir.
        tmp_dir (str): Path to temp dir.
    """
    if moved and os.path.exists(tmp_dir):
        shutil.move(tmp_dir, ckpt_dir)


def make_args(**overrides) -> SimpleNamespace:
    """
    Helper to create a fully-populated SimpleNamespace for CLI argument mocks in tests.
    Accepts overrides for any CLI argument.
    Returns:
        SimpleNamespace: Namespace with all CLI args set.
    """
    args = {
        "minutes": 0.01,
        "test_mode": True,
        "model": "mlp",
        "data_dir": "./mnist_data",
        "checkpoint_dir": "./checkpoints",
        "batch_size": 64,
        "conv1_out": 16,
        "conv2_out": 32,
        "conv3_out": 32,
        "fc_out": 10,
        "compile": False,
    }
    args.update(overrides)
    return SimpleNamespace(**args)


# Direct function call tests (fast, granular coverage)
def test_train_main_direct():
    """Test direct call to train_main with minimal args."""
    args = make_args(test_mode=True)
    train_main(args)


def test_infer_main_direct():
    """Test direct call to infer_main with minimal args."""
    args = make_args(test_mode=True)
    infer_main(args)


def test_validate_main_direct():
    """Test direct call to validate_main with minimal args."""
    args = make_args(test_mode=True)
    validate_main(args)


def test_infer_main_no_checkpoint():
    """Test infer_main with no checkpoint present (should not crash)."""
    moved, ckpt_dir, tmp_dir = _temporarily_move_checkpoints()
    try:
        args = make_args(test_mode=True)
        infer_main(args)
    finally:
        _restore_checkpoints(moved, ckpt_dir, tmp_dir)


def test_validate_main_no_checkpoint():
    """
    Test validate_main with no checkpoint present (should log error and return early).
    """
    ckpt_dir = "./checkpoints"
    tmp_dir = "./checkpoints_tmp"
    if os.path.exists(ckpt_dir):
        shutil.move(ckpt_dir, tmp_dir)
    try:
        args = make_args(test_mode=True)
        validate_main(args)
    finally:
        if os.path.exists(tmp_dir):
            shutil.move(tmp_dir, ckpt_dir)


def test_validate_main_test_and_normal(monkeypatch):
    """
    Test validate_main in both test-mode and normal mode (with checkpoint present).
    """
    import tempfile

    from torch import optim

    from mnist_train import SimpleMNISTModel, save_checkpoint
    from mnist_validate import validate_main

    # Create a dummy checkpoint
    with tempfile.TemporaryDirectory() as tmp_ckpt:
        model = SimpleMNISTModel()
        optimizer = optim.SGD(model.parameters(), lr=0.01)
        save_checkpoint(model, optimizer, 1, checkpoint_dir=tmp_ckpt)
        # Patch load_latest_checkpoint to use our temp dir
        monkeypatch.setattr(
            "mnist_validate.load_latest_checkpoint",
            lambda m, optimizer, checkpoint_dir=tmp_ckpt, model_type=None: 1,
        )
        args = make_args(test_mode=True)
        validate_main(args)
        args = make_args(test_mode=False)
        validate_main(args)


def test_train_main_time_limit(monkeypatch):
    """
    Test train_main time-limited branch (simulate time limit reached).
    """
    import time as time_module

    from mnist_train import train_main

    # Patch time to simulate time passing
    original_time = time_module.time
    times = [original_time(), original_time() + 1000]
    monkeypatch.setattr(
        time_module, "time", lambda: times.pop(0) if times else original_time()
    )
    args = make_args(minutes=0.00001, test_mode=True)
    train_main(args=args)


def test_train_main_time_limit_warning_and_complete(monkeypatch, caplog):
    """
    Test train_main for logger.warning and logger.info("Training session complete.") branches.
    """
    import time as time_module

    from mnist_train import train_main

    # Patch time to simulate time limit reached on first epoch
    original_time = time_module.time
    times = [original_time(), original_time() + 1000]
    monkeypatch.setattr(
        time_module, "time", lambda: times.pop(0) if times else original_time()
    )

    args = make_args(minutes=0.00001, test_mode=True)

    caplog.set_level("WARNING", logger="mnist_train")
    caplog.set_level("INFO", logger="mnist_train")
    train_main(args=args)
    assert "Training stopped after" in caplog.text
    assert "Training session complete." in caplog.text


def test_validate_main_empty_loader(monkeypatch, caplog):
    """
    Test validate_main for accuracy==0.0 branch (empty loader).
    """
    from mnist_validate import validate_main

    # Patch load_latest_checkpoint to return a model
    monkeypatch.setattr(
        "mnist_validate.load_latest_checkpoint", lambda m, optimizer, checkpoint_dir=None, model_type=None: 1
    )
    # Patch get_full_test_loader to return empty loader
    monkeypatch.setattr(
        "mnist_validate.get_full_test_loader",
        lambda batch_size=64, data_dir=None: __import__("torch").utils.data.DataLoader([]),
    )

    args = make_args(test_mode=False)

    with caplog.at_level("INFO"):
        validate_main(args)
    assert "Validation accuracy on 0 test samples: 0.0000" in caplog.text


def test_validate_main_no_checkpoint_error(monkeypatch, caplog):
    """
    Test validate_main for logger.error branch in load_latest_checkpoint (no checkpoint found).
    """
    from mnist_validate import validate_main

    # Patch load_latest_checkpoint to simulate no checkpoint
    def fake_load_latest_checkpoint(model, optimizer, checkpoint_dir=None, model_type=None):
        import logging
        logger = logging.getLogger("mnist_validate")
        logger.error("No checkpoint found. Please train the model first.")
        return None

    monkeypatch.setattr(
        "mnist_validate.load_latest_checkpoint", fake_load_latest_checkpoint
    )

    args = make_args(test_mode=False)

    with caplog.at_level("ERROR"):
        validate_main(args)
    assert "No checkpoint found. Please train the model first." in caplog.text


def test_train_main_saves_model_specific_checkpoints(tmp_path, monkeypatch):
    """
    Test that train_main saves model-specific checkpoints for both MLP and CNN.
    """
    import os

    from mnist_train import train_main

    class Args:
        """Arguments for simulating CLI input in tests."""

        minutes = 0.01
        test_mode = True
        model = "mlp"
        data_dir = "./mnist_data"
        checkpoint_dir = "./checkpoints"
        batch_size = 64

    # Use a temp checkpoint dir
    ckpt_dir = tmp_path / "checkpoints"

    def patched_save_checkpoint(
        model,
        optimizer,
        epoch,
        _checkpoint_dir,
        _tag=None,
        model_type="mlp",  # pylint: disable=too-many-arguments, too-many-positional-arguments
    ):
        """Patched save_checkpoint for test isolation."""
        os.makedirs(ckpt_dir, exist_ok=True)
        __import__("torch").save(
            {
                "epoch": epoch,
                "model_state_dict": model.state_dict(),
                "optimizer_state_dict": optimizer.state_dict(),
            },
            str(ckpt_dir / f"mnist_{model_type}_epoch_{epoch}.pt"),
        )

    monkeypatch.setattr("mnist_train.save_checkpoint", patched_save_checkpoint)
    # MLP
    Args.model = "mlp"
    train_main(args=Args())
    mlp_ckpts = list(ckpt_dir.glob("mnist_mlp_epoch_*.pt"))
    assert mlp_ckpts, "MLP checkpoint not found!"
    # CNN
    Args.model = "cnn"
    train_main(args=Args())
    cnn_ckpts = list(ckpt_dir.glob("mnist_cnn_epoch_*.pt"))
    assert cnn_ckpts, "CNN checkpoint not found!"


def test_validate_main_model_selection(caplog):
    """
    Test validate_main for both MLP and CNN model selection and checkpoint loading.
    """
    from mnist_validate import validate_main

    class Args:
        """Arguments for simulating CLI input in tests."""

        test_mode = True
        model = "mlp"

    caplog.set_level("INFO", logger="mnist_validate")
    # MLP
    Args.model = "mlp"
    validate_main(args=Args())
    assert "Using SimpleMNISTModel (MLP) model." in caplog.text
    # CNN
    Args.model = "cnn"
    validate_main(args=Args())
    assert "Using MinimalMNISTCNN (compact CNN) model." in caplog.text


def test_infer_main_model_selection(caplog):
    """
    Test infer_main for both MLP and CNN model selection and checkpoint loading.
    """
    from mnist_infer import infer_main

    class Args:
        """Arguments for simulating CLI input in tests."""

        test_mode = True
        model = "mlp"

    caplog.set_level("INFO", logger="mnist_infer")
    # MLP
    Args.model = "mlp"
    infer_main(args=Args())
    assert "Using SimpleMNISTModel (MLP) model." in caplog.text
    # CNN
    Args.model = "cnn"
    infer_main(args=Args())
    assert "Using MinimalMNISTCNN (compact CNN) model." in caplog.text


def test_checkpoint_architecture_mismatch(tmp_path, caplog):
    """
    Test that loading a checkpoint with mismatched architecture raises ValueError and logs an error.
    """
    from torch import optim

    from mnist_cnn import MinimalMNISTCNN
    from mnist_train import load_latest_checkpoint, save_checkpoint

    # Save checkpoint with one architecture
    model1 = MinimalMNISTCNN(conv1_out=8, conv2_out=16, conv3_out=16, fc_out=10)
    optimizer1 = optim.SGD(model1.parameters(), lr=0.01)
    save_checkpoint(
        model1, optimizer1, epoch=1, checkpoint_dir=tmp_path, model_type="cnn"
    )
    # Try to load with a different architecture
    model2 = MinimalMNISTCNN(conv1_out=16, conv2_out=32, conv3_out=32, fc_out=10)
    optimizer2 = optim.SGD(model2.parameters(), lr=0.01)
    caplog.set_level("ERROR", logger="mnist_train")
    with pytest.raises(ValueError) as excinfo:
        load_latest_checkpoint(
            model2, optimizer2, checkpoint_dir=tmp_path, model_type="cnn"
        )
    assert "Model architecture mismatch!" in caplog.text
    assert "does not match checkpoint" in str(excinfo.value)


# Subprocess tests for CLI parsing only
@pytest.mark.slow
def test_train_script_cli():
    """Test train script CLI interface with subprocess."""
    result = subprocess.run(
        [sys.executable, "mnist_train.py", "--minutes", "0.001", "--test-mode"],
        capture_output=True,
        text=True,
        check=True,
    )
    assert result.returncode == 0
    assert (
        "Training session complete" in result.stdout
        or "Training session complete" in result.stderr
    )


@pytest.mark.slow
def test_infer_script_cli():
    """Test infer script CLI interface with subprocess."""
    # Remove checkpoints to test error path
    ckpt_dir = "./checkpoints"
    tmp_dir = "./checkpoints_tmp"
    if os.path.exists(ckpt_dir):
        shutil.move(ckpt_dir, tmp_dir)
    try:
        result = subprocess.run(
            [sys.executable, "mnist_infer.py", "--test-mode"],
            capture_output=True,
            text=True,
            check=True,
        )
        assert result.returncode == 0
        assert (
            "Loaded checkpoint" in result.stdout
            or "Loaded checkpoint" in result.stderr
            or "No checkpoint found" in result.stdout
            or "No checkpoint found" in result.stderr
            or "Please train the model first" in result.stdout
            or "Please train the model first" in result.stderr
        )
    finally:
        if os.path.exists(tmp_dir):
            shutil.move(tmp_dir, ckpt_dir)


@pytest.mark.slow
def test_validate_script_cli():
    """Test validate script CLI interface with subprocess."""
    # Remove checkpoints to test error path
    ckpt_dir = "./checkpoints"
    tmp_dir = "./checkpoints_tmp"
    if os.path.exists(ckpt_dir):
        shutil.move(ckpt_dir, tmp_dir)
    try:
        result = subprocess.run(
            [sys.executable, "mnist_validate.py", "--test-mode"],
            capture_output=True,
            text=True,
            check=True,
        )
        assert result.returncode == 0
        assert (
            "Validation accuracy" in result.stdout
            or "Validation accuracy" in result.stderr
            or "No checkpoint found" in result.stdout
            or "No checkpoint found" in result.stderr
            or "Please train the model first" in result.stdout
            or "Please train the model first" in result.stderr
        )
    finally:
        if os.path.exists(tmp_dir):
            shutil.move(tmp_dir, ckpt_dir)
