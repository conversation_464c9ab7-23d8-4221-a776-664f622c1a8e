# pylint: disable=R0801
"""Utility script to upgrade old MNIST checkpoints with model config metadata.

Note: This module contains code duplication with other CLI scripts for argument parsing.
This is acceptable for CLI utilities to maintain independence and clarity.
"""

# Disable duplicate code warnings - CLI argument parsing patterns are intentionally shared

import argparse
import glob
import os

import torch

from mnist_cnn import MinimalMNISTCNN
from mnist_train import SimpleMNISTModel


def get_model_config(model, model_type):
    """
    Extract model configuration for checkpointing.

    Args:
        model: The model instance
        model_type: Type of model ('cnn' or 'mlp')

    Returns:
        Dict: Model configuration dictionary
    """
    if model_type == "cnn":
        return {
            "conv1_out": (
                model.conv1[0].out_channels
                if hasattr(model, "conv1") and model.conv1
                else 16
            ),
            "conv2_out": (
                model.conv2[0].out_channels
                if hasattr(model, "conv2") and model.conv2
                else 32
            ),
            "conv3_out": (
                model.conv3[0].out_channels
                if hasattr(model, "conv3") and model.conv3
                else 32
            ),
            "fc_out": model.fc1.out_features if hasattr(model, "fc1") else 10,
        }
    return {"type": "mlp"}


def upgrade_checkpoints(
    checkpoint_dir: str,
    model_type: str,
):
    """
    Upgrade old checkpoints to include model configuration metadata.

    Args:
        checkpoint_dir: Directory containing checkpoints
        model_type: Type of model ('cnn' or 'mlp')
    """
    pattern = os.path.join(checkpoint_dir, f"mnist_{model_type}_epoch_*.pt")
    files = glob.glob(pattern)
    if not files:
        print(f"No checkpoints found in {checkpoint_dir} for model type {model_type}.")
        return

    if model_type == "cnn":
        model = MinimalMNISTCNN()
    else:
        model = SimpleMNISTModel()

    config = get_model_config(model, model_type)

    # Main processing loop
    for checkpoint_file in files:
        ckpt = torch.load(checkpoint_file)
        if "model_config" in ckpt:
            print(f"[SKIP] {checkpoint_file} already has model_config.")
            continue
        ckpt["model_config"] = config
        torch.save(ckpt, checkpoint_file)
        print(f"[UPGRADED] {checkpoint_file} now has model_config: {config}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Upgrade old checkpoints to include model_config."
    )
    parser.add_argument(
        "--checkpoint-dir",
        type=str,
        default="./checkpoints",
        help="Directory with checkpoints",
    )
    parser.add_argument(
        "--model", type=str, default="cnn", choices=["cnn", "mlp"], help="Model type"
    )
    args = parser.parse_args()
    upgrade_checkpoints(
        args.checkpoint_dir,
        args.model,
    )
