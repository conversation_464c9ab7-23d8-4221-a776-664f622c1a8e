"""Test suite for MinimalMNISTCNN model and related helpers."""

# Group and order imports: standard library, third-party, then local imports.
# Alphabetize within each group. No imports inside functions/classes.
import random

import numpy as np
import pytest
import torch
from torch.utils.data import DataLoader, Subset
from torchvision import datasets, transforms

# Consolidated top-level imports to remove reimports
# Note: Removed test/helper file imports as their functions are already imported at top

from mnist_cnn import (
    MinimalMNISTCNN,
    evaluate_model,
    train_one_epoch,
)


random.seed(42)
torch.manual_seed(42)
np.random.seed(42)


def get_small_mnist_loaders(batch_size: int = 8, num_samples: int = 100) -> tuple:
    """
    Get small MNIST DataLoaders for testing.
    Args:
        batch_size (int): Batch size for loaders.
        num_samples (int): Number of samples for train/test.
    Returns:
        tuple: (train_loader, test_loader)
    """
    transform = transforms.Compose(
        [
            transforms.ToTensor(),
        ]
    )
    train_dataset = datasets.MNIST(
        root="./mnist_data", train=True, download=True, transform=transform
    )
    test_dataset = datasets.MNIST(
        root="./mnist_data", train=False, download=True, transform=transform
    )
    train_dataset = Subset(train_dataset, range(num_samples))
    test_dataset = Subset(test_dataset, range(num_samples))
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    return train_loader, test_loader


def test_cnn_output_shape():
    """Test that CNN model produces correct output shape."""
    model = MinimalMNISTCNN()
    x = torch.randn(8, 1, 28, 28)
    output = model(x)
    assert output.shape == (8, 10), f"Expected output shape (8, 10), got {output.shape}"


def test_cnn_training_step_decreases_loss():
    """Test that CNN training step decreases loss."""
    model = MinimalMNISTCNN()
    train_loader, _ = get_small_mnist_loaders(batch_size=8, num_samples=32)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    criterion = torch.nn.CrossEntropyLoss()
    model.train()
    images, labels = next(iter(train_loader))
    outputs = model(images)
    loss1 = criterion(outputs, labels)
    optimizer.zero_grad()
    loss1.backward()
    optimizer.step()
    outputs = model(images)
    loss2 = criterion(outputs, labels)
    assert (
        loss2.item() <= loss1.item() + 1e-3
    ), f"Loss did not decrease or exploded: {loss1.item()} -> {loss2.item()}"


def test_cnn_evaluate_model_accuracy():
    """Test that CNN evaluation returns valid accuracy."""
    model = MinimalMNISTCNN()
    _, test_loader = get_small_mnist_loaders(batch_size=8, num_samples=32)
    acc = evaluate_model(model, test_loader)
    assert 0.0 <= acc <= 1.0, f"Accuracy should be between 0 and 1, got {acc}"


@pytest.mark.slow
def test_cnn_trains_to_high_accuracy():
    """Test that CNN can train to high accuracy (slow test)."""
    # This test is slow and only for demonstration; it checks if the model can reach
    # >98% accuracy in a few epochs
    train_loader, test_loader = get_small_mnist_loaders(batch_size=32, num_samples=1024)
    model = MinimalMNISTCNN()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    for _ in range(10):
        train_one_epoch(model, train_loader, optimizer)
    acc = evaluate_model(model, test_loader)
    assert acc > 0.98, f"Expected >98% accuracy, got {acc}"


@pytest.mark.skipif(
    not hasattr(torch, "compile"), reason="torch.compile requires PyTorch 2.x+"
)
def test_cnn_torch_compile_output_shape():
    """Test that torch.compile CNN produces correct output shape."""
    model = MinimalMNISTCNN()
    compiled_model = torch.compile(model)
    x = torch.randn(8, 1, 28, 28)
    output = compiled_model(x)
    assert output.shape == (8, 10)


@pytest.mark.skipif(
    not hasattr(torch, "compile"), reason="torch.compile requires PyTorch 2.x+"
)
def test_cnn_torch_compile_training_step():
    """Test that torch.compile CNN can perform training step."""
    train_loader, _ = get_small_mnist_loaders(batch_size=8, num_samples=32)
    model = MinimalMNISTCNN()
    compiled_model = torch.compile(model)
    optimizer = torch.optim.Adam(compiled_model.parameters(), lr=0.01)
    criterion = torch.nn.CrossEntropyLoss()
    compiled_model.train()
    images, labels = next(iter(train_loader))
    outputs = compiled_model(images)
    loss1 = criterion(outputs, labels)
    optimizer.zero_grad()
    loss1.backward()
    optimizer.step()
    outputs = compiled_model(images)
    loss2 = criterion(outputs, labels)
    assert (
        loss2.item() <= loss1.item() + 1e-3
    ), f"Loss did not decrease or exploded: {loss1.item()} -> {loss2.item()}"
